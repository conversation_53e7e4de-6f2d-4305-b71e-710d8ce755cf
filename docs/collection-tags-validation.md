# Collection Tags Validation

## 概述

本功能实现了在保存 collection tags 到数据库时的验证机制，确保只有在 scattering.io API 中定义的有效 tags 才会被保存到数据库中。

## 功能特性

### API 验证
- **API 端点**: `https://api.scattering.io/api/v3/collections/tags`
- **响应格式**: `CollectionsTagsViewsResponse` 结构体
- **验证机制**: 只保存 API 返回的有效 tag names

### 数据过滤
- **过滤条件**: 
  1. Tag name 不能为空
  2. Tag name 必须存在于 API 返回的有效 tags 列表中
- **性能优化**: 使用 `map[string]bool` 进行快速查找

## 实现细节

### 核心方法

#### `fetchValidCollectionTags()`
```go
func (d *Database) fetchValidCollectionTags() (map[string]bool, error)
```

**功能**:
- 调用 scattering.io API 获取有效的 collection tags
- 解析响应为 `CollectionsTagsViewsResponse` 结构体
- 创建 `map[string]bool` 用于快速查找有效 tag names

**错误处理**:
- HTTP 请求失败
- 非 200 状态码响应
- JSON 解析失败

#### `SaveCollectionTags()` 修改
在原有方法基础上添加了验证逻辑：

```go
// 获取有效 tags
validTagsMap, err := d.fetchValidCollectionTags()
if err != nil {
    return fmt.Errorf("failed to fetch valid collection tags: %w", err)
}

// 在处理每个 tag 时验证
if tagName != "" && validTagsMap[tagName] {
    // 只保存有效的 tags
    collectionTag := CollectionTag{
        TagName:         tagName,
        TwitterUsername: twitterUsername,
    }
    collectionTags = append(collectionTags, collectionTag)
}
```

### 数据结构

#### `CollectionsTagsViewsResponse`
```go
type CollectionsTagsViewsResponse struct {
    Code int `json:"code"`
    Data struct {
        List []struct {
            Name  string `json:"name"`
            Color string `json:"color"`
            Rank  int    `json:"rank"`
            Type  int    `json:"type"`
        } `json:"list"`
    } `json:"data"`
    Msg string `json:"msg"`
}
```

## 使用场景

### 数据清洗
- 过滤掉无效或过时的 tag names
- 确保数据库中只存储标准化的 tags
- 防止因外部数据源错误导致的数据污染

### 性能优化
- 减少数据库中的无效数据
- 提高查询效率
- 降低存储空间占用

## 错误处理

### API 调用失败
如果 API 调用失败，整个 `SaveCollectionTags` 操作会失败并返回错误：
```go
return fmt.Errorf("failed to fetch valid collection tags: %w", err)
```

### 网络超时
HTTP 客户端配置了 30 秒超时，与项目中其他 API 调用保持一致。

## 测试

### 单元测试
- `TestCollectionTagsFiltering`: 验证数据结构和过滤逻辑
- 测试覆盖有效和无效 tag names 的处理

### 集成测试建议
1. 模拟 API 响应测试完整流程
2. 测试网络错误情况的处理
3. 验证数据库事务的正确性

## 监控和维护

### 日志记录
建议添加以下日志记录：
- API 调用成功/失败
- 过滤掉的无效 tags 数量
- 保存的有效 tags 数量

### 性能监控
- API 响应时间
- 过滤效率
- 数据库操作性能

## 配置选项

### HTTP 超时
当前设置为 30 秒，可根据需要调整：
```go
client := &http.Client{Timeout: 30 * time.Second}
```

### 错误重试
目前没有实现重试机制，如需要可以添加指数退避重试逻辑。
