package db

import (
	"testing"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// TestTelegramNotificationOperations tests the notification tracking functionality
// This test verifies that the database operations work correctly for preventing duplicate notifications
func TestTelegramNotificationOperations(t *testing.T) {
	// Setup in-memory SQLite database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Create a simplified table for testing
	err = db.Exec(`
		CREATE TABLE tweets (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			tweet_id VARCHAR(255) UNIQUE,
			telegram_notification_sent BOOLEAN DEFAULT FALSE,
			telegram_notification_sent_at DATETIME
		)
	`).Error
	if err != nil {
		t.Fatalf("Failed to create test table: %v", err)
	}

	database := &Database{DB: db}

	// Test 1: Check status for non-existent tweet
	_, err = database.CheckTelegramNotificationStatus("nonexistent")
	if err == nil {
		t.Error("Expected error for nonexistent tweet")
	}

	// Test 2: Insert a tweet and check initial status
	err = db.Exec("INSERT INTO tweets (tweet_id, telegram_notification_sent) VALUES (?, ?)", "123456789", false).Error
	if err != nil {
		t.Fatalf("Failed to insert test tweet: %v", err)
	}

	sent, err := database.CheckTelegramNotificationStatus("123456789")
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if sent {
		t.Error("Expected initial notification status to be false")
	}

	// Test 3: Mark notification as sent
	err = database.MarkTelegramNotificationSent("123456789")
	if err != nil {
		t.Errorf("Unexpected error marking notification as sent: %v", err)
	}

	// Test 4: Verify status is now true
	sent, err = database.CheckTelegramNotificationStatus("123456789")
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	if !sent {
		t.Error("Expected notification status to be true after marking")
	}

	// Test 5: Verify timestamp was set
	var sentAt *time.Time
	err = db.Raw("SELECT telegram_notification_sent_at FROM tweets WHERE tweet_id = ?", "123456789").Scan(&sentAt).Error
	if err != nil {
		t.Errorf("Failed to fetch timestamp: %v", err)
	}
	if sentAt == nil {
		t.Error("Expected timestamp to be set")
	}
	if time.Since(*sentAt) > time.Minute {
		t.Error("Expected timestamp to be recent")
	}

	// Test 6: Try to mark as sent again (should still work)
	err = database.MarkTelegramNotificationSent("123456789")
	if err != nil {
		t.Errorf("Unexpected error marking notification as sent again: %v", err)
	}

	t.Log("All notification tracking tests passed successfully")
}

// TestCollectionTagsFiltering tests the collection tags filtering functionality
func TestCollectionTagsFiltering(t *testing.T) {
	// This test demonstrates the expected behavior of the SaveCollectionTags method
	// with valid tags filtering based on API response

	// Test data with both valid and invalid tags
	collections := []interface{}{
		map[string]interface{}{
			"twitter_username": "test_user",
			"tags": []interface{}{
				map[string]interface{}{"name": "defi"},    // should be valid
				map[string]interface{}{"name": "gaming"},  // should be valid
				map[string]interface{}{"name": "invalid"}, // should be filtered out
				map[string]interface{}{"name": ""},        // should be filtered out (empty)
			},
		},
	}

	// Expected behavior: only tags that exist in the API response should be saved
	// The actual API call will determine which tags are valid

	// Verify test data structure
	if len(collections) != 1 {
		t.Errorf("Expected 1 collection, got %d", len(collections))
	}

	collection := collections[0].(map[string]interface{})
	tags := collection["tags"].([]interface{})

	if len(tags) != 4 {
		t.Errorf("Expected 4 tags in test data, got %d", len(tags))
	}

	// Count non-empty tag names
	nonEmptyTags := 0
	for _, tagInterface := range tags {
		if tagMap, ok := tagInterface.(map[string]interface{}); ok {
			if tagName, ok := tagMap["name"].(string); ok && tagName != "" {
				nonEmptyTags++
			}
		}
	}

	if nonEmptyTags != 3 {
		t.Errorf("Expected 3 non-empty tags, got %d", nonEmptyTags)
	}

	t.Log("Collection tags filtering test structure validated successfully")
}
